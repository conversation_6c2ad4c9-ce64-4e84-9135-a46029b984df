import os
import re
import subprocess
import tempfile
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from gtts import gTTS  # Google TTS
import base64
import requests
import time
from urllib.parse import urlencode
import hashlib
import hmac
import random
import traceback
import json  # 新增导入json模块用于配置保存


class VideoEditorApp:
    def __init__(self, master):
        self.master = master
        self.master.title("智能视频剪辑工具")
        self.master.geometry("800x700")  # 增加窗口高度以容纳新控件

        # 初始化文件列表
        self.video_files = []
        self.subtitle_files = []

        # 加载配置
        self.load_config()

        # --- GUI 配置部分 ---
        config_frame = ttk.LabelFrame(master, text="TTS配置")
        config_frame.pack(padx=10, pady=5, fill="x")

        ttk.Label(config_frame, text="TTS服务:").grid(row=0, column=0, padx=5, pady=5)
        self.tts_service = tk.StringVar(value=self.config.get('service', 'Google'))
        tts_combo = ttk.Combobox(config_frame, textvariable=self.tts_service,
                                 values=["Google", "Tencent"])
        tts_combo.grid(row=0, column=1, padx=5, pady=5, sticky="w")
        tts_combo.bind("<<ComboboxSelected>>", self.update_voice_options)

        # 音色选择控件
        ttk.Label(config_frame, text="音色:").grid(row=0, column=2, padx=5, pady=5)
        self.voice_type = tk.StringVar(value=self.config.get('voice', '智瑜(女) - 101016'))
        self.voice_combo = ttk.Combobox(config_frame, textvariable=self.voice_type, width=15)
        self.voice_combo.grid(row=0, column=3, padx=5, pady=5, sticky="w")

        # 自定义音色ID输入框 (默认隐藏)
        self.custom_voice_frame = ttk.Frame(config_frame)
        self.custom_voice_frame.grid(row=0, column=4, padx=5, pady=5, sticky="w")
        self.custom_voice_frame.grid_remove()  # 初始隐藏

        ttk.Label(self.custom_voice_frame, text="音色ID:").grid(row=0, column=0, padx=(0, 5))
        self.custom_voice_id = tk.StringVar(value=self.config.get('custom_voice_id', '101016'))
        self.custom_voice_entry = ttk.Entry(self.custom_voice_frame, textvariable=self.custom_voice_id, width=8)
        self.custom_voice_entry.grid(row=0, column=1)

        # 初始化音色选项
        self.update_voice_options()

        tencent_frame = ttk.Frame(config_frame)
        tencent_frame.grid(row=1, column=0, columnspan=4, sticky="w", padx=5, pady=5)

        ttk.Label(tencent_frame, text="SecretId:").grid(row=0, column=0)
        self.tencent_id = tk.StringVar(value=self.config.get('tencent_id', ''))
        ttk.Entry(tencent_frame, textvariable=self.tencent_id, width=30).grid(row=0, column=1)

        ttk.Label(tencent_frame, text="SecretKey:").grid(row=1, column=0)
        self.tencent_key = tk.StringVar(value=self.config.get('tencent_key', ''))
        ttk.Entry(tencent_frame, textvariable=self.tencent_key, width=30, show="*").grid(row=1, column=1)

        ttk.Label(tencent_frame, text="AppId:").grid(row=2, column=0)
        self.tencent_appid = tk.StringVar(value=self.config.get('tencent_appid', ''))
        ttk.Entry(tencent_frame, textvariable=self.tencent_appid, width=30).grid(row=2, column=1)

        # 保存配置按钮
        ttk.Button(tencent_frame, text="保存配置", command=self.save_config).grid(row=3, column=1, pady=5, sticky="e")

        # 测试音色按钮
        ttk.Button(tencent_frame, text="测试音色", command=self.test_voice).grid(row=3, column=2, pady=5, sticky="e")

        # 清理缓存按钮
        ttk.Button(tencent_frame, text="清理缓存", command=self.clean_cache).grid(row=3, column=3, pady=5, sticky="e")

        # GPU加速配置（简化版）
        gpu_frame = ttk.LabelFrame(master, text="GPU加速")
        gpu_frame.pack(padx=10, pady=5, fill="x")

        ttk.Label(gpu_frame, text="GPU加速:").grid(row=0, column=0, padx=5, pady=5)
        self.gpu_acceleration = tk.StringVar(value=self.config.get('gpu_acceleration', 'auto'))
        gpu_combo = ttk.Combobox(gpu_frame, textvariable=self.gpu_acceleration,
                                values=["auto", "disabled"], width=10, state="readonly")
        gpu_combo.grid(row=0, column=1, padx=5, pady=5, sticky="w")

        # GPU状态显示
        self.gpu_status_var = tk.StringVar(value="检测中...")
        ttk.Label(gpu_frame, text="状态:").grid(row=0, column=2, padx=5, pady=5)
        self.gpu_status_label = ttk.Label(gpu_frame, textvariable=self.gpu_status_var, foreground="blue")
        self.gpu_status_label.grid(row=0, column=3, padx=5, pady=5, sticky="w")

        # 初始化GPU检测
        self.detect_gpu()

        # CPU使用率监控配置
        self.cpu_monitor_enabled = True
        self.max_cpu_usage = 80  # CPU使用率安全阀：80%

        upload_frame = ttk.LabelFrame(master, text="文件上传")
        upload_frame.pack(padx=10, pady=5, fill="x")

        ttk.Button(upload_frame, text="选择视频文件", command=self.select_videos).grid(row=0, column=0, padx=5, pady=5)
        ttk.Button(upload_frame, text="选择字幕文件", command=self.select_subtitles).grid(row=0, column=1, padx=5,
                                                                                          pady=5)
        ttk.Button(upload_frame, text="批量添加文件对", command=self.batch_add).grid(row=0, column=2, padx=5, pady=5)

        list_frame = ttk.Frame(master)
        list_frame.pack(padx=10, pady=5, fill="both", expand=True)

        columns = ("视频文件", "字幕文件", "状态")
        self.file_tree = ttk.Treeview(list_frame, columns=columns, show="headings")

        # 设置列标题和宽度
        self.file_tree.heading("视频文件", text="视频文件")
        self.file_tree.heading("字幕文件", text="字幕文件")
        self.file_tree.heading("状态", text="状态")

        self.file_tree.column("视频文件", width=250)
        self.file_tree.column("字幕文件", width=250)
        self.file_tree.column("状态", width=120)

        self.file_tree.pack(fill="both", expand=True, pady=5)

        # 添加右键菜单
        self.context_menu = tk.Menu(self.file_tree, tearoff=0)
        self.context_menu.add_command(label="删除选中项", command=self.delete_selected_files)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="查看文件路径", command=self.show_file_path)

        # 绑定右键点击事件
        self.file_tree.bind("<Button-3>", self.show_context_menu)

        self.progress = ttk.Progressbar(master, orient="horizontal", length=400, mode="determinate")
        self.progress.pack(pady=10)

        self.status_var = tk.StringVar(value="准备就绪")
        status_label = ttk.Label(master, textvariable=self.status_var)
        status_label.pack(pady=5)

        btn_frame = ttk.Frame(master)
        btn_frame.pack(pady=10)

        ttk.Button(btn_frame, text="开始处理", command=self.start_processing).grid(row=0, column=0, padx=5)
        ttk.Button(btn_frame, text="清除列表", command=self.clear_lists).grid(row=0, column=1, padx=5)
        ttk.Button(btn_frame, text="退出", command=master.quit).grid(row=0, column=2, padx=5)

    def update_voice_options(self, event=None):
        """根据选择的TTS服务更新音色选项"""
        service = self.tts_service.get()
        if service == "Google":
            # Google TTS的音色选项（实际上Google TTS不支持选择音色，这里提供语言选项）
            voices = [
                "中文(普通话)",
                "中文(粤语)",
                "英语(美国)",
                "英语(英国)",
                "日语",
                "韩语"
            ]
            self.voice_combo['values'] = voices
            self.voice_type.set("中文(普通话)")
            # 隐藏自定义输入框
            self.custom_voice_frame.grid_remove()
        elif service == "Tencent":
            # 腾讯云长文本语音合成音色选项 (基于官方文档2025-08-15更新)
            voices = [
                "=== 大模型音色 ===",
                "智斌(阅读男声) - 501000",
                "智兰(资讯女声) - 501001",
                "智菊(阅读女声) - 501002",
                "智宇(阅读男声) - 501003",
                "月华(聊天女声) - 501004",
                "飞镜(聊天男声) - 501005",
                "千嶂(聊天男声) - 501006",
                "浅草(聊天男声) - 501007",
                "WeJames(外语男声) - 501008",
                "WeWinny(外语女声) - 501009",
                "爱小童(男童声) - 601015",
                "爱小溪(聊天女声) - 601000",
                "爱小洛(阅读女声) - 601001",
                "爱小辰(聊天男声) - 601002",
                "爱小荷(阅读女声) - 601003",
                "爱小树(资讯男声) - 601004",
                "爱小静(聊天女声) - 601005",
                "爱小耀(阅读男声) - 601006",
                "爱小叶(聊天女声) - 601007",
                "爱小豪(聊天男声) - 601008",
                "爱小芊(聊天女声) - 601009",
                "爱小娇(聊天女声) - 601010",
                "爱小川(聊天男声) - 601011",
                "爱小璟(特色女声) - 601012",
                "爱小伊(阅读女声) - 601013",
                "爱小简(聊天男声) - 601014",
                "=== 精品音色 ===",
                "智逍遥(阅读男声) - 100510000",
                "智瑜(情感女声) - 101001",
                "智聆(通用女声) - 101002",
                "智美(客服女声) - 101003",
                "智云(通用男声) - 101004",
                "智莉(通用女声) - 101005",
                "智言(助手女声) - 101006",
                "智琪(客服女声) - 101008",
                "智芸(知性女声) - 101009",
                "智华(通用男声) - 101010",
                "智燕(新闻女声) - 101011",
                "智丹(新闻女声) - 101012",
                "智辉(新闻男声) - 101013",
                "智宁(新闻男声) - 101014",
                "智萌(男童声) - 101015",
                "智甜(女童声) - 101016",
                "智蓉(情感女声) - 101017",
                "智靖(情感男声) - 101018",
                "智彤(粤语女声) - 101019",
                "智刚(新闻男声) - 101020",
                "智瑞(新闻男声) - 101021",
                "智虹(新闻女声) - 101022",
                "智萱(聊天女声) - 101023",
                "智皓(聊天男声) - 101024",
                "智薇(聊天女声) - 101025",
                "智希(通用女声) - 101026",
                "智梅(通用女声) - 101027",
                "智洁(通用女声) - 101028",
                "智凯(通用男声) - 101029",
                "智柯(通用男声) - 101030",
                "智奎(通用男声) - 101031",
                "智芳(通用女声) - 101032",
                "智蓓(客服女声) - 101033",
                "智莲(通用女声) - 101034",
                "智依(通用女声) - 101035",
                "智川(四川女声) - 101040",
                "WeJack(英文男声) - 101050",
                "WeRose(英文女声) - 101051",
                "智味(通用男声) - 101052",
                "智方(通用男声) - 101053",
                "智友(通用男声) - 101054",
                "智付(通用女声) - 101055",
                "智林(东北男声) - 101056",
                "智美子(日语女声) - 101057",
                "智英(客服女声) - 101080",
                "智佳(客服女声) - 101081",
                "=== 301系列精品音色 ===",
                "爱小广(通用男声) - 301000",
                "爱小栋(通用男声) - 301001",
                "爱小海(通用男声) - 301002",
                "爱小霞(通用女声) - 301003",
                "爱小玲(通用女声) - 301004",
                "爱小章(通用男声) - 301005",
                "爱小峰(通用男声) - 301006",
                "爱小亮(通用男声) - 301007",
                "爱小博(通用男声) - 301008",
                "爱小芸(通用女声) - 301009",
                "爱小秋(通用女声) - 301010",
                "爱小芳(通用女声) - 301011",
                "爱小琴(通用女声) - 301012",
                "爱小康(通用男声) - 301013",
                "爱小辉(通用男声) - 301014",
                "爱小璐(通用女声) - 301015",
                "爱小阳(通用男声) - 301016",
                "爱小泉(通用男声) - 301017",
                "爱小昆(通用男声) - 301018",
                "爱小诚(通用男声) - 301019",
                "爱小岚(通用女声) - 301020",
                "爱小茹(通用女声) - 301021",
                "爱小蓉(通用女声) - 301022",
                "爱小燕(通用女声) - 301023",
                "爱小莲(通用女声) - 301024",
                "爱小武(通用男声) - 301025",
                "爱小雪(通用女声) - 301026",
                "爱小媛(通用女声) - 301027",
                "爱小娴(通用女声) - 301028",
                "爱小涛(通用男声) - 301029",
                "爱小溪(客服女声) - 301030",
                "爱小树(聊天男声) - 301031",
                "爱小荷(聊天女声) - 301032",
                "爱小叶(客服女声) - 301033",
                "爱小杭(聊天男声) - 301034",
                "爱小梅(聊天女声) - 301035",
                "爱小柯(聊天男声) - 301036",
                "爱小静(聊天女声) - 301037",
                "爱小桃(资讯女声) - 301038",
                "爱小萌(聊天女声) - 301039",
                "爱小星(特色男声) - 301040",
                "爱小菲(聊天女声) - 301041",
                "=== 标准音色 ===",
                "智逍遥(阅读男声) - 10510000",
                "智瑜(情感女声) - 1001",
                "智聆(通用女声) - 1002",
                "智美(客服女声) - 1003",
                "智云(通用男声) - 1004",
                "智莉(通用女声) - 1005",
                "智琪(客服女声) - 1008",
                "智芸(知性女声) - 1009",
                "智华(通用男声) - 1010",
                "智蓉(情感女声) - 1017",
                "智靖(情感男声) - 1018",
                "WeJack(英文男声) - 1050",
                "WeRose(英文女声) - 1051",
                "自定义ID"  # 新增自定义选项
            ]
            self.voice_combo['values'] = voices
            self.voice_type.set(self.config.get('voice', '智兰(资讯女声) - 501001'))

            # 检查当前是否选择了"自定义ID"
            if self.voice_type.get() == "自定义ID":
                self.custom_voice_frame.grid()
            else:
                self.custom_voice_frame.grid_remove()

    def save_config(self):
        """保存配置到文件"""
        self.config = {
            'service': self.tts_service.get(),
            'voice': self.voice_type.get(),
            'custom_voice_id': self.custom_voice_id.get(),  # 保存自定义音色ID
            'tencent_id': self.tencent_id.get(),
            'tencent_key': self.tencent_key.get(),
            'tencent_appid': self.tencent_appid.get(),
            'gpu_acceleration': self.gpu_acceleration.get()
        }

        try:
            with open('tts_config.json', 'w') as f:
                json.dump(self.config, f)
            messagebox.showinfo("成功", "配置已保存！")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")

    def test_voice(self):
        """测试当前选择的音色是否可用"""
        if self.tts_service.get() != "Tencent":
            messagebox.showinfo("提示", "音色测试仅支持腾讯云TTS服务")
            return

        # 检查配置
        if not all([self.tencent_id.get(), self.tencent_key.get(), self.tencent_appid.get()]):
            messagebox.showerror("错误", "请先配置腾讯云TTS参数")
            return

        try:
            import os

            # 在项目缓存目录创建临时文件
            cache_dir = self.create_cache_dir()
            temp_audio_path = os.path.join(cache_dir, "voice_test.mp3")

            # 测试文本
            test_text = "这是音色测试，您好！"

            # 显示测试状态
            self.status_var.set("正在测试音色...")
            self.master.update()

            # 生成测试音频
            self.generate_tts(test_text, temp_audio_path)

            # 测试成功
            self.status_var.set("音色测试成功！")
            messagebox.showinfo("测试成功", f"音色测试成功！\n音色: {self.voice_type.get()}\n测试音频已生成")

            # 清理临时文件
            try:
                os.unlink(temp_audio_path)
            except:
                pass

        except Exception as e:
            self.status_var.set("音色测试失败")
            messagebox.showerror("测试失败", f"音色测试失败:\n{str(e)}")

    def clean_cache(self):
        """清理项目缓存文件"""
        try:
            import shutil
            import glob

            # 获取项目缓存目录
            cache_dir = self.create_cache_dir()

            # 统计清理的文件
            cleaned_files = 0
            cleaned_size = 0

            if os.path.exists(cache_dir):
                # 遍历缓存目录中的所有文件和文件夹
                for item in os.listdir(cache_dir):
                    item_path = os.path.join(cache_dir, item)
                    try:
                        if os.path.isfile(item_path):
                            size = os.path.getsize(item_path)
                            os.remove(item_path)
                            cleaned_files += 1
                            cleaned_size += size
                        elif os.path.isdir(item_path):
                            # 计算目录大小
                            dir_size = 0
                            file_count = 0
                            for root, dirs, files in os.walk(item_path):
                                for file in files:
                                    file_path = os.path.join(root, file)
                                    if os.path.exists(file_path):
                                        dir_size += os.path.getsize(file_path)
                                        file_count += 1

                            # 删除目录
                            shutil.rmtree(item_path)
                            cleaned_files += file_count
                            cleaned_size += dir_size
                    except (OSError, PermissionError) as e:
                        print(f"无法删除 {item_path}: {e}")
                        continue  # 跳过无法删除的文件

            # 转换文件大小为可读格式
            if cleaned_size > 1024 * 1024:
                size_str = f"{cleaned_size / (1024 * 1024):.1f} MB"
            elif cleaned_size > 1024:
                size_str = f"{cleaned_size / 1024:.1f} KB"
            else:
                size_str = f"{cleaned_size} bytes"

            cache_path = os.path.relpath(cache_dir)
            self.status_var.set(f"缓存清理完成: {cleaned_files} 个文件, {size_str}")
            messagebox.showinfo("清理完成",
                f"缓存清理完成！\n\n"
                f"缓存目录: {cache_path}\n"
                f"清理文件: {cleaned_files} 个\n"
                f"释放空间: {size_str}")

        except Exception as e:
            self.status_var.set("缓存清理失败")
            messagebox.showerror("清理失败", f"缓存清理失败:\n{str(e)}")

    def load_config(self):
        """从文件加载配置"""
        self.config = {}
        try:
            if os.path.exists('tts_config.json'):
                with open('tts_config.json', 'r') as f:
                    self.config = json.load(f)
        except:
            # 如果配置文件损坏，使用默认配置
            self.config = {
                'service': 'Google',
                'voice': '智兰(资讯女声) - 501001',
                'custom_voice_id': '501001',
                'tencent_id': '',
                'tencent_key': '',
                'tencent_appid': '',
                'gpu_acceleration': 'auto'
            }

    def select_videos(self):
        files = filedialog.askopenfilenames(filetypes=[("视频文件", "*.mp4 *.avi *.mov")])
        if files:
            # 直接添加文件，不进行复杂验证
            for file in files:
                self.video_files.append(file)

            self.update_file_tree()
            self.status_var.set(f"已添加 {len(files)} 个视频文件")

    def select_subtitles(self):
        files = filedialog.askopenfilenames(filetypes=[("字幕文件", "*.srt")])
        if files:
            # 直接添加文件，不进行复杂验证
            for file in files:
                self.subtitle_files.append(file)

            self.update_file_tree()
            self.status_var.set(f"已添加 {len(files)} 个字幕文件")

    def batch_add(self):
        dir_path = filedialog.askdirectory(title="选择包含视频和字幕文件的文件夹")
        if not dir_path:
            return

        video_extensions = ('.mp4', '.avi', '.mov')
        srt_extensions = ('.srt',)
        all_files = [f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))]
        video_files_in_dir = [f for f in all_files if f.lower().endswith(video_extensions)]
        srt_files_in_dir = [f for f in all_files if f.lower().endswith(srt_extensions)]

        added_pairs = 0
        added_videos = 0
        added_subtitles = 0

        # 添加所有视频文件
        for v_file in video_files_in_dir:
            self.video_files.append(os.path.join(dir_path, v_file))
            added_videos += 1

        # 添加所有字幕文件
        for s_file in srt_files_in_dir:
            self.subtitle_files.append(os.path.join(dir_path, s_file))
            added_subtitles += 1

        # 统计配对数量
        for v_file in video_files_in_dir:
            base_name = os.path.splitext(v_file)[0]
            matching_srt = [s for s in srt_files_in_dir if os.path.splitext(s)[0] == base_name]
            if matching_srt:
                added_pairs += 1

        self.update_file_tree()

        # 在状态栏显示结果
        self.status_var.set(f"批量添加: {added_videos}个视频, {added_subtitles}个字幕, {added_pairs}组配对")

    def update_file_tree(self):
        self.file_tree.delete(*self.file_tree.get_children())

        # 创建文件配对字典
        paired_files = {}

        # 添加所有视频文件
        for v in self.video_files:
            v_basename = os.path.basename(v)
            paired_files[v_basename] = [v, ""]

        # 尝试匹配字幕文件
        for s in self.subtitle_files:
            s_basename = os.path.basename(s)
            s_key = os.path.splitext(s_basename)[0]

            # 寻找匹配的视频文件
            matched = False
            for v_key in paired_files:
                if os.path.splitext(v_key)[0] == s_key:
                    paired_files[v_key][1] = s
                    matched = True
                    break

            # 如果没有匹配的视频文件，单独显示字幕文件
            if not matched:
                paired_files[s_basename] = ["", s]

        # 显示所有文件（包括未配对的）
        for file_name, (v_path, s_path) in paired_files.items():
            video_display = os.path.basename(v_path) if v_path else "❌ 未选择"
            subtitle_display = os.path.basename(s_path) if s_path else "❌ 未选择"

            # 添加状态指示
            if v_path and s_path:
                status = "✓ 已配对"
            elif v_path:
                status = "⚠ 缺少字幕"
            elif s_path:
                status = "⚠ 缺少视频"
            else:
                status = "❌ 错误"

            self.file_tree.insert("", "end", values=(
                video_display,
                subtitle_display,
                status
            ))

        # 更新状态显示
        total_pairs = len([1 for _, (v, s) in paired_files.items() if v and s])
        total_files = len(paired_files)
        self.status_var.set(f"已上传 {total_files} 组文件，其中 {total_pairs} 组已配对")

    def clear_lists(self):
        self.video_files = []
        self.subtitle_files = []
        self.update_file_tree()

    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            # 选择点击的项目
            item = self.file_tree.identify_row(event.y)
            if item:
                self.file_tree.selection_set(item)
                self.context_menu.post(event.x_root, event.y_root)
        except:
            pass

    def delete_selected_files(self):
        """删除选中的文件"""
        selected_items = self.file_tree.selection()
        if not selected_items:
            self.status_var.set("请先选择要删除的文件")
            return

        # 直接删除，不需要确认弹窗
        deleted_count = 0
        for item in selected_items:
            values = self.file_tree.item(item, 'values')
            video_name = values[0] if values[0] != "❌ 未选择" else None
            subtitle_name = values[1] if values[1] != "❌ 未选择" else None

            # 从列表中删除对应文件
            if video_name:
                self.video_files = [f for f in self.video_files if os.path.basename(f) != video_name]
                deleted_count += 1
            if subtitle_name:
                self.subtitle_files = [f for f in self.subtitle_files if os.path.basename(f) != subtitle_name]
                deleted_count += 1

        self.update_file_tree()
        self.status_var.set(f"已删除 {deleted_count} 个文件")

    def show_file_path(self):
        """显示文件完整路径"""
        selected_items = self.file_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择文件")
            return

        item = selected_items[0]
        values = self.file_tree.item(item, 'values')
        video_name = values[0] if values[0] != "❌ 未选择" else None
        subtitle_name = values[1] if values[1] != "❌ 未选择" else None

        info = "文件路径信息:\n\n"
        if video_name:
            video_path = next((f for f in self.video_files if os.path.basename(f) == video_name), None)
            info += f"视频文件: {video_path}\n\n"
        if subtitle_name:
            subtitle_path = next((f for f in self.subtitle_files if os.path.basename(f) == subtitle_name), None)
            info += f"字幕文件: {subtitle_path}\n"

        messagebox.showinfo("文件路径", info)

    def run_subprocess_safe(self, cmd, timeout=30, **kwargs):
        """安全运行subprocess，处理Windows编码问题"""
        # 设置Windows环境下的启动信息
        startupinfo = None
        if os.name == 'nt':
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW

        # 设置默认参数
        default_kwargs = {
            'capture_output': True,
            'encoding': 'utf-8',
            'errors': 'ignore',
            'startupinfo': startupinfo,
            'timeout': timeout
        }
        default_kwargs.update(kwargs)

        try:
            return subprocess.run(cmd, **default_kwargs)
        except subprocess.TimeoutExpired:
            print(f"命令执行超时: {' '.join(cmd[:3])}...")
            raise
        except Exception as e:
            print(f"命令执行失败: {e}")
            raise

    def validate_video_file(self, file_path):
        """简单验证视频文件"""
        try:
            # 基本检查：文件存在、大小、扩展名
            if not os.path.exists(file_path):
                return False

            file_size = os.path.getsize(file_path)
            if file_size < 1024:  # 小于1KB
                return False

            valid_extensions = ('.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv')
            return file_path.lower().endswith(valid_extensions)

        except Exception:
            return False

    def validate_subtitle_file(self, file_path):
        """简单验证字幕文件"""
        try:
            # 基本检查：文件存在、大小、扩展名
            if not os.path.exists(file_path):
                return False

            if not file_path.lower().endswith('.srt'):
                return False

            file_size = os.path.getsize(file_path)
            return file_size > 0

        except Exception:
            return False

    def detect_gpu(self):
        """检测可用的GPU硬件加速"""
        try:
            print("开始检测GPU硬件加速...")

            # 检测NVIDIA GPU
            nvidia_available = self.check_gpu_encoder("h264_nvenc")
            print(f"NVIDIA GPU检测: {'可用' if nvidia_available else '不可用'}")

            # 检测AMD GPU
            amd_available = self.check_gpu_encoder("h264_amf")
            print(f"AMD GPU检测: {'可用' if amd_available else '不可用'}")

            # 检测Intel GPU
            intel_available = self.check_gpu_encoder("h264_qsv")
            print(f"Intel GPU检测: {'可用' if intel_available else '不可用'}")

            # 测试GPU硬件加速是否真正可用
            gpu_info = []
            if nvidia_available and self.test_gpu_acceleration("cuda", "h264_nvenc"):
                gpu_info.append("NVIDIA")
            if amd_available and self.test_gpu_acceleration("dxva2", "h264_amf"):
                gpu_info.append("AMD")
            if intel_available and self.test_gpu_acceleration("qsv", "h264_qsv"):
                gpu_info.append("Intel")

            if gpu_info:
                status = f"可用: {', '.join(gpu_info)}"
                self.gpu_status_var.set(status)
                self.gpu_status_label.configure(foreground="green")
                print(f"GPU加速检测完成: {status}")
            else:
                self.gpu_status_var.set("GPU加速不可用，将使用CPU")
                self.gpu_status_label.configure(foreground="orange")
                print("警告: 未检测到可用的GPU加速，将使用CPU处理")

        except Exception as e:
            self.gpu_status_var.set(f"检测失败: {str(e)}")
            self.gpu_status_label.configure(foreground="red")
            print(f"GPU检测失败: {e}")

    def test_gpu_acceleration(self, hwaccel, encoder):
        """测试GPU加速是否真正可用"""
        try:
            import tempfile
            import os

            # 创建测试用的临时目录
            cache_dir = self.create_cache_dir()
            test_input = os.path.join(cache_dir, "gpu_test_input.mp4")
            test_output = os.path.join(cache_dir, "gpu_test_output.mp4")

            # 创建简单的测试视频
            create_cmd = [
                "ffmpeg", "-f", "lavfi", "-i", "testsrc=duration=1:size=320x240:rate=1",
                "-c:v", "libx264", "-y", test_input
            ]
            result = self.run_subprocess_safe(create_cmd, timeout=10)

            if result.returncode != 0:
                return False

            # 测试GPU编码
            test_cmd = [
                "ffmpeg", "-hwaccel", hwaccel, "-i", test_input,
                "-c:v", encoder, "-t", "1", "-y", test_output
            ]
            result = self.run_subprocess_safe(test_cmd, timeout=10)

            # 清理测试文件
            try:
                if os.path.exists(test_input):
                    os.remove(test_input)
                if os.path.exists(test_output):
                    os.remove(test_output)
            except:
                pass

            success = result.returncode == 0
            print(f"GPU加速测试 ({hwaccel}/{encoder}): {'成功' if success else '失败'}")
            return success

        except Exception as e:
            print(f"GPU加速测试失败 ({hwaccel}/{encoder}): {e}")
            return False

    def monitor_cpu_usage(self):
        """监控CPU使用率"""
        try:
            import psutil
            cpu_percent = psutil.cpu_percent(interval=1)
            return cpu_percent
        except ImportError:
            # 如果没有psutil，使用简单的方法
            return 0
        except Exception:
            return 0

    def wait_for_cpu_available(self):
        """等待CPU使用率降到安全范围内"""
        if not self.cpu_monitor_enabled:
            return

        try:
            import psutil
            while True:
                cpu_percent = psutil.cpu_percent(interval=1)
                if cpu_percent <= self.max_cpu_usage:
                    break
                print(f"CPU使用率过高 ({cpu_percent:.1f}%)，等待降低到 {self.max_cpu_usage}% 以下...")
                self.status_var.set(f"等待CPU使用率降低... 当前: {cpu_percent:.1f}%")
                self.master.update()
                time.sleep(2)
        except ImportError:
            print("警告: 未安装psutil，无法监控CPU使用率")
        except Exception as e:
            print(f"CPU监控失败: {e}")

    def run_subprocess_with_cpu_limit(self, cmd, **kwargs):
        """运行subprocess并监控CPU使用率"""
        self.wait_for_cpu_available()
        return self.run_subprocess_safe(cmd, **kwargs)

    def check_gpu_encoder(self, encoder):
        """检查特定GPU编码器是否可用"""
        try:
            result = self.run_subprocess_safe([
                "ffmpeg", "-hide_banner", "-encoders"
            ], timeout=10, text=True)

            if result.returncode == 0 and result.stdout:
                return encoder in result.stdout
            else:
                return False
        except subprocess.TimeoutExpired:
            print(f"检测编码器超时: {encoder}")
            return False
        except Exception as e:
            print(f"检测编码器失败: {encoder}, 错误: {e}")
            return False

    def get_ffmpeg_gpu_params(self, need_reencode=False):
        """根据配置获取FFmpeg GPU加速参数"""
        gpu_mode = self.gpu_acceleration.get()

        print(f"获取GPU参数: 模式={gpu_mode}, 需要重编码={need_reencode}")

        if gpu_mode == "disabled":
            print("GPU加速已禁用，使用CPU处理")
            if need_reencode:
                return {
                    'decoder': [],
                    'encoder': ['-c:v', 'libx264', '-preset', 'veryfast'],
                    'hwaccel': []
                }
            else:
                return {
                    'decoder': [],
                    'encoder': ['-c:v', 'copy'],
                    'hwaccel': []
                }

        # 自动检测GPU加速
        if gpu_mode == "auto":
            # 优先使用NVIDIA GPU
            if self.test_gpu_acceleration("cuda", "h264_nvenc"):
                print("使用NVIDIA GPU加速 (CUDA + NVENC)")
                if need_reencode:
                    return {
                        'decoder': ['-hwaccel', 'cuda', '-hwaccel_output_format', 'cuda'],
                        'encoder': ['-c:v', 'h264_nvenc', '-preset', 'fast'],
                        'hwaccel': ['-hwaccel', 'cuda']
                    }
                else:
                    return {
                        'decoder': ['-hwaccel', 'cuda'],
                        'encoder': ['-c:v', 'copy'],
                        'hwaccel': ['-hwaccel', 'cuda']
                    }
            # 其次使用Intel GPU
            elif self.test_gpu_acceleration("qsv", "h264_qsv"):
                print("使用Intel GPU加速 (QSV)")
                if need_reencode:
                    return {
                        'decoder': ['-hwaccel', 'qsv', '-hwaccel_output_format', 'qsv'],
                        'encoder': ['-c:v', 'h264_qsv', '-preset', 'fast'],
                        'hwaccel': ['-hwaccel', 'qsv']
                    }
                else:
                    return {
                        'decoder': ['-hwaccel', 'qsv'],
                        'encoder': ['-c:v', 'copy'],
                        'hwaccel': ['-hwaccel', 'qsv']
                    }
            # 最后使用AMD GPU
            elif self.test_gpu_acceleration("dxva2", "h264_amf"):
                print("使用AMD GPU加速 (DXVA2 + AMF)")
                if need_reencode:
                    return {
                        'decoder': ['-hwaccel', 'dxva2'],
                        'encoder': ['-c:v', 'h264_amf', '-quality', 'speed'],
                        'hwaccel': ['-hwaccel', 'dxva2']
                    }
                else:
                    return {
                        'decoder': ['-hwaccel', 'dxva2'],
                        'encoder': ['-c:v', 'copy'],
                        'hwaccel': ['-hwaccel', 'dxva2']
                    }
            else:
                print("GPU加速不可用，回退到CPU处理")
                if need_reencode:
                    return {
                        'decoder': [],
                        'encoder': ['-c:v', 'libx264', '-preset', 'veryfast'],
                        'hwaccel': []
                    }
                else:
                    return {
                        'decoder': [],
                        'encoder': ['-c:v', 'copy'],
                        'hwaccel': []
                    }

        # 如果不是auto模式，回退到CPU
        print("使用CPU处理")
        if need_reencode:
            return {
                'decoder': [],
                'encoder': ['-c:v', 'libx264', '-preset', 'veryfast'],
                'hwaccel': []
            }
        else:
            return {
                'decoder': [],
                'encoder': ['-c:v', 'copy'],
                'hwaccel': []
            }

    def gpu_benchmark(self):
        """GPU性能基准测试"""
        import tempfile
        import threading

        def run_benchmark():
            try:
                self.gpu_status_var.set("正在进行性能测试...")
                self.gpu_status_label.configure(foreground="blue")

                tmp_dir = self.create_temp_dir()
                try:
                    # 创建测试视频
                    test_video = os.path.join(tmp_dir, "test.mp4")
                    subprocess.run([
                        "ffmpeg", "-f", "lavfi", "-i", "testsrc=duration=5:size=1280x720:rate=30",
                        "-c:v", "libx264", "-y", test_video
                    ], capture_output=True, timeout=30)

                    # 测试CPU编码
                    cpu_start = time.time()
                    cpu_output = os.path.join(tmp_dir, "cpu_test.mp4")
                    subprocess.run([
                        "ffmpeg", "-i", test_video, "-c:v", "libx264", "-preset", "veryfast",
                        "-y", cpu_output
                    ], capture_output=True, timeout=30)
                    cpu_time = time.time() - cpu_start

                    # 测试GPU编码
                    gpu_params = self.get_ffmpeg_gpu_params()
                    if gpu_params['encoder'] != ['-c:v', 'libx264', '-preset', 'veryfast']:
                        gpu_start = time.time()
                        gpu_output = os.path.join(tmp_dir, "gpu_test.mp4")
                        cmd = (["ffmpeg"] + gpu_params['decoder'] + ["-i", test_video] +
                               gpu_params['encoder'] + ["-y", gpu_output])
                        subprocess.run(cmd, capture_output=True, timeout=30)
                        gpu_time = time.time() - gpu_start

                        speedup = cpu_time / gpu_time if gpu_time > 0 else 1
                        result = f"CPU: {cpu_time:.2f}s, GPU: {gpu_time:.2f}s, 加速: {speedup:.1f}x"
                    else:
                        result = f"CPU: {cpu_time:.2f}s, GPU不可用"

                    self.gpu_status_var.set(result)
                    self.gpu_status_label.configure(foreground="green")

                finally:
                    # 清理临时目录
                    try:
                        if os.path.exists(tmp_dir):
                            import shutil
                            shutil.rmtree(tmp_dir)
                    except Exception:
                        pass  # 忽略清理错误

            except Exception as e:
                self.gpu_status_var.set(f"测试失败: {str(e)}")
                self.gpu_status_label.configure(foreground="red")

        # 在后台线程运行测试
        threading.Thread(target=run_benchmark, daemon=True).start()

    def start_processing(self):
        # 保存当前配置
        self.save_config()

        tree_items = self.file_tree.get_children()
        if not tree_items:
            messagebox.showerror("错误", "请先上传视频和字幕文件！")
            return

        # 检查配对的文件
        files_to_process = []
        unpaired_files = []

        for item in tree_items:
            values = self.file_tree.item(item, 'values')
            video_name = values[0] if values[0] != "❌ 未选择" else None
            subtitle_name = values[1] if values[1] != "❌ 未选择" else None
            status = values[2]

            if video_name and subtitle_name and status == "✓ 已配对":
                # 找到完整路径
                video_path = next((f for f in self.video_files if os.path.basename(f) == video_name), None)
                subtitle_path = next((f for f in self.subtitle_files if os.path.basename(f) == subtitle_name), None)

                if video_path and subtitle_path and os.path.exists(video_path) and os.path.exists(subtitle_path):
                    files_to_process.append((video_path, subtitle_path))
                else:
                    unpaired_files.append(f"{video_name} + {subtitle_name}")
            else:
                if video_name:
                    unpaired_files.append(f"{video_name} (缺少字幕)")
                if subtitle_name:
                    unpaired_files.append(f"{subtitle_name} (缺少视频)")

        if not files_to_process:
            messagebox.showerror("错误", "没有找到已配对的视频和字幕文件！\n请确保视频和字幕文件名匹配。")
            return

        # 如果有未配对的文件，询问用户是否继续
        if unpaired_files:
            unpaired_list = "\n".join(unpaired_files[:5])  # 最多显示5个
            if len(unpaired_files) > 5:
                unpaired_list += f"\n... 还有 {len(unpaired_files) - 5} 个文件"

            result = messagebox.askyesno(
                "发现未配对文件",
                f"发现 {len(unpaired_files)} 个未配对的文件:\n\n{unpaired_list}\n\n"
                f"已配对文件: {len(files_to_process)} 组\n\n"
                "是否继续处理已配对的文件？"
            )
            if not result:
                return

        self.progress["maximum"] = len(files_to_process)

        for i, (video_path, subtitle_path) in enumerate(files_to_process):
            try:
                video_name = os.path.basename(video_path)
                self.status_var.set(f"处理中 ({i + 1}/{len(files_to_process)}): {video_name}")
                self.master.update()

                self.process_video_custom_logic(video_path, subtitle_path)

                self.progress["value"] = i + 1
                self.master.update()
            except Exception as e:
                error_details = traceback.format_exc()
                print(f"处理文件 {video_name} 时发生严重错误: {e}\n{error_details}")
                messagebox.showerror("处理错误", f"处理文件 {video_name} 时出错:\n{e}\n\n详细信息请查看控制台输出。")
                break  # 出现错误时停止后续处理

        self.status_var.set("处理完成！")
        messagebox.showinfo("完成", "所有文件处理完成！")

    def parse_srt_and_adjust(self, srt_path):
        subtitles = self.parse_srt(srt_path)
        if not subtitles:
            return []

        # 步骤 1: 将第一条字幕的开始时间后移0.1秒
        original_start = subtitles[0]["start"]
        subtitles[0]["start"] += 0.1

        if subtitles[0]["start"] >= subtitles[0]["end"]:
            print(
                f"警告: 调整后，第一条字幕的开始时间({subtitles[0]['start']:.3f}s)已超过结束时间({subtitles[0]['end']:.3f}s)。")
            subtitles[0]["end"] = subtitles[0]["start"] + 0.1  # 确保至少有0.1s时长

        print(
            f"第一条字幕时间已从 {original_start:.3f}s 调整为: start={subtitles[0]['start']:.3f}s, end={subtitles[0]['end']:.3f}s")
        return subtitles

    def create_cache_dir(self):
        """创建项目缓存目录"""
        cache_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "cache")
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
        return cache_dir

    def create_temp_dir(self):
        """在缓存目录中创建临时工作目录"""
        cache_dir = self.create_cache_dir()
        import uuid
        temp_name = f"temp_{uuid.uuid4().hex[:8]}"
        temp_dir = os.path.join(cache_dir, temp_name)
        os.makedirs(temp_dir, exist_ok=True)
        return temp_dir

    def process_video_custom_logic(self, video_path, srt_path):
        tmp_dir = self.create_temp_dir()
        try:
            start_time = time.time()
            print(f"\n{'=' * 20}\n开始处理视频: {os.path.basename(video_path)}\n{'=' * 20}")
            print(f"GPU加速状态: {self.gpu_acceleration.get()}")
            print(f"临时文件目录: {tmp_dir}")

            # --- 步骤 1: 字幕处理 ---
            adjusted_subtitles = self.parse_srt_and_adjust(srt_path)
            if not adjusted_subtitles:
                print("字幕文件为空或解析失败，跳过处理。")
                return

            # 生成输出字幕文件路径
            output_dir = os.path.dirname(video_path)
            base_name = os.path.splitext(os.path.basename(video_path))[0]
            output_srt = os.path.join(output_dir, f"{base_name}_out.srt")

            # 写入调整后的字幕文件
            self.write_adjusted_srt(adjusted_subtitles, output_srt)
            print(f"已生成调整后的字幕文件: {output_srt}")

            video_duration = self.get_video_duration(video_path)
            video_only_path = os.path.join(tmp_dir, "video_only.mp4")

            # 获取GPU加速参数
            gpu_params = self.get_ffmpeg_gpu_params()
            print(f"使用GPU加速: {self.gpu_acceleration.get()}")

            # 提取视频流（去除原始音频）- 使用GPU加速
            cmd = ["ffmpeg"] + gpu_params['hwaccel'] + ["-i", video_path, "-c:v", "copy", "-an", "-y", video_only_path]
            print(f"执行命令: {' '.join(cmd[:5])}...")
            self.run_subprocess_with_cpu_limit(cmd, check=True)

            # --- 步骤 2: 视频裁剪 ---
            print("\n--- 步骤2: 正在按字幕开始时间切割视频...")
            cut_points = [0] + [sub["start"] for sub in adjusted_subtitles] + [video_duration]
            video_clips = []
            for i in range(len(cut_points) - 1):
                start = cut_points[i]
                end = cut_points[i + 1]
                if end <= start: continue

                clip_path = os.path.join(tmp_dir, f"raw_clip_{i + 1}.mp4")
                # 重新编码以确保干净切割 - 使用GPU加速
                reencode_gpu_params = self.get_ffmpeg_gpu_params(need_reencode=True)

                cmd = (["ffmpeg"] + reencode_gpu_params['decoder'] + ["-i", video_only_path,
                       "-ss", str(start), "-to", str(end)] + reencode_gpu_params['encoder'] + ["-crf", "22",
                       "-c:a", "aac", "-ar", "44100", "-ac", "2", "-b:a", "128k",
                       "-y", clip_path])
                print(f"执行视频切割命令: {' '.join(cmd[:5])}...")
                self.run_subprocess_with_cpu_limit(cmd, check=True)
                video_clips.append(clip_path)
                print(f"  已生成并转码视频片段 {i + 1}: from {start:.3f}s to {end:.3f}s")

            # --- 步骤 3: TTS配音 ---
            print("\n--- 步骤3: 正在为每条字幕生成配音...")
            print("    使用腾讯云长文本语音合成，支持超自然大模型音色")
            audio_files = []
            for i, sub in enumerate(adjusted_subtitles):
                audio_path = os.path.join(tmp_dir, f"audio_{i + 1}.mp3")
                print(f"    正在生成第 {i + 1}/{len(adjusted_subtitles)} 条音频: '{sub['text'][:30]}...'")
                self.generate_tts(sub["text"], audio_path)
                # 转换音频为AAC格式以确保兼容性
                aac_audio_path = os.path.join(tmp_dir, f"audio_{i + 1}.aac")
                subprocess.run([
                    "ffmpeg", "-i", audio_path,
                    "-c:a", "aac", "-b:a", "128k", "-ar", "44100", "-ac", "2",
                    "-y", aac_audio_path
                ], check=True, capture_output=True)
                audio_files.append(aac_audio_path)
                print(f"  已生成音频片段 {i + 1} for '{sub['text'][:30]}...'")

            # --- 步骤 4: 音画匹配与合并 ---
            print("\n--- 步骤4: 正在进行音画匹配并生成最终片段...")
            final_clips_for_concat = []

            # 4.1. 处理第一段视频 (添加静音音频)
            if video_clips:
                # 为第一个视频片段添加静音音频
                first_clip_with_audio = os.path.join(tmp_dir, "first_clip_with_audio.mp4")
                subprocess.run([
                    "ffmpeg", "-i", video_clips[0],
                    "-f", "lavfi", "-i", "anullsrc=channel_layout=stereo:sample_rate=44100",
                    "-c:v", "copy", "-c:a", "aac", "-b:a", "128k", "-shortest",
                    "-y", first_clip_with_audio
                ], check=True, capture_output=True)
                final_clips_for_concat.append(first_clip_with_audio)
                print(f"  片段1 (无配音) 已添加静音音频。")

            # 4.2. 循环处理后续音视频对
            for i in range(len(audio_files)):
                video_segment_path = video_clips[i + 1]
                audio_segment_path = audio_files[i]
                output_segment_path = os.path.join(tmp_dir, f"final_clip_{i + 2}.mp4")

                video_dur = self.get_video_duration(video_segment_path)
                audio_dur = self.get_video_duration(audio_segment_path)

                if video_dur == 0:
                    print(f"  警告: 视频片段 {i + 2} 时长为0，已跳过。")
                    continue

                ratio = audio_dur / video_dur
                print(
                    f"\n  正在处理片段 {i + 2}: V_dur={video_dur:.3f}s, A_dur={audio_dur:.3f}s, Ratio={ratio:.3f}")

                if ratio <= 1:
                    print("    策略: 补充静音到音频末尾。")
                    padded_audio_path = os.path.join(tmp_dir, f"padded_audio_{i + 1}.aac")
                    # 计算需要补充的静音时长
                    pad_duration = video_dur - audio_dur
                    # 生成静音片段
                    subprocess.run([
                        "ffmpeg", "-f", "lavfi", "-i",
                        f"anullsrc=channel_layout=stereo:sample_rate=44100:duration={pad_duration}",
                        "-c:a", "aac", "-b:a", "128k", "-y", os.path.join(tmp_dir, "silence.aac")
                    ], check=True, capture_output=True)
                    # 拼接原始音频和静音
                    concat_list = os.path.join(tmp_dir, "concat.txt")
                    with open(concat_list, "w") as f:
                        f.write(f"file '{audio_segment_path}'\n")
                        f.write(f"file '{os.path.join(tmp_dir, 'silence.aac')}'\n")
                    subprocess.run([
                        "ffmpeg", "-f", "concat", "-safe", "0", "-i", concat_list,
                        "-c", "copy", "-y", padded_audio_path
                    ], check=True, capture_output=True)

                    # 合并视频和音频
                    subprocess.run([
                        "ffmpeg", "-i", video_segment_path, "-i", padded_audio_path,
                        "-c:v", "copy", "-c:a", "copy",
                        "-map", "0:v", "-map", "1:a",
                        "-shortest", "-y", output_segment_path
                    ], check=True, capture_output=True)

                elif 1 < ratio <= 1.1:
                    print(f"    策略: 音频加速 {ratio:.3f} 倍。")
                    sped_up_audio_path = os.path.join(tmp_dir, f"spedup_audio_{i + 1}.aac")
                    subprocess.run([
                        "ffmpeg", "-i", audio_segment_path,
                        "-filter:a", f"atempo={ratio:.4f}",
                        "-c:a", "aac", "-b:a", "128k", "-y", sped_up_audio_path
                    ], check=True, capture_output=True)

                    # 合并视频和加速后的音频
                    subprocess.run([
                        "ffmpeg", "-i", video_segment_path, "-i", sped_up_audio_path,
                        "-c:v", "copy", "-c:a", "copy",
                        "-map", "0:v", "-map", "1:a",
                        "-shortest", "-y", output_segment_path
                    ], check=True, capture_output=True)

                else:  # ratio > 1.1
                    print(f"    策略: 视频降速以匹配音频时长。")
                    # 使用滤镜降低视频速度 - 需要重新编码，不能使用copy模式
                    reencode_gpu_params = self.get_ffmpeg_gpu_params(need_reencode=True)

                    cmd = (["ffmpeg"] + reencode_gpu_params['decoder'] + ["-i", video_segment_path, "-i", audio_segment_path,
                           "-filter_complex", f"[0:v]setpts={ratio:.4f}*PTS[v]",
                           "-map", "[v]", "-map", "1:a",
                           "-c:a", "copy"] + reencode_gpu_params['encoder'] + ["-crf", "22",
                           "-shortest", "-y", output_segment_path])

                    print(f"执行视频降速命令: {' '.join(cmd[:5])}...")
                    self.run_subprocess_with_cpu_limit(cmd, check=True)

                final_clips_for_concat.append(output_segment_path)
                print(f"  片段 {i + 2} 处理完成，已添加到时间轴。")

            # --- 最终合并 ---
            if final_clips_for_concat:
                print("\n--- 所有片段处理完毕，正在合并输出最终视频...")
                output_file = os.path.join(output_dir, f"{base_name}_output.mp4")
                self.concat_videos(final_clips_for_concat, output_file)

                # 计算处理时间
                end_time = time.time()
                processing_time = end_time - start_time
                print(f"处理完成！最终文件保存在: {output_file}")
                print(f"总处理时间: {processing_time:.2f}秒")

                # 显示GPU加速效果
                if self.gpu_acceleration.get() != "disabled":
                    print(f"✓ 已使用GPU加速处理")
                else:
                    print("○ 使用CPU处理")

            print("--- 临时文件已自动清除。")

        except Exception as e:
            print(f"处理视频时发生错误: {e}")
            raise e
        finally:
            # 清理临时目录
            try:
                if os.path.exists(tmp_dir):
                    import shutil
                    shutil.rmtree(tmp_dir)
                    print(f"已清理临时目录: {tmp_dir}")
            except Exception as cleanup_error:
                print(f"清理临时目录失败: {cleanup_error}")

    def write_adjusted_srt(self, subtitles, output_path):
        """将调整后的字幕列表写入SRT文件"""
        srt_content = ""
        for i, sub in enumerate(subtitles):
            # 将秒转换为SRT时间格式 (时:分:秒,毫秒)
            start_time = self.seconds_to_time(sub["start"])
            end_time = self.seconds_to_time(sub["end"])

            srt_content += f"{i + 1}\n"
            srt_content += f"{start_time} --> {end_time}\n"
            srt_content += f"{sub['text']}\n\n"

        with open(output_path, "w", encoding="utf-8") as f:
            f.write(srt_content)
        print(f"已生成字幕文件: {output_path}")

    @staticmethod
    def seconds_to_time(total_seconds):
        """将秒数转换为SRT格式的时间字符串 (HH:MM:SS,mmm)"""
        hours = int(total_seconds // 3600)
        minutes = int((total_seconds % 3600) // 60)
        seconds = int(total_seconds % 60)
        milliseconds = int((total_seconds - int(total_seconds)) * 1000)

        return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"

    def get_video_duration(self, video_path):
        try:
            result = self.run_subprocess_safe([
                "ffprobe", "-v", "error", "-show_entries", "format=duration",
                "-of", "default=noprint_wrappers=1:nokey=1", video_path
            ], timeout=30, text=True)

            if result.returncode != 0:
                print(f"警告: ffprobe返回错误代码 {result.returncode} for {os.path.basename(video_path)}")
                return 0.0

            return float(result.stdout.strip())
        except subprocess.TimeoutExpired:
            print(f"警告: 获取视频时长超时 {os.path.basename(video_path)}")
            return 0.0
        except (ValueError, IndexError):
            print(f"警告: 无法解析视频时长 {os.path.basename(video_path)}")
            return 0.0
        except Exception as e:
            print(f"警告: 获取视频时长失败 {os.path.basename(video_path)}: {e}")
            return 0.0

    def parse_srt(self, srt_path):
        subtitles = []
        try:
            with open(srt_path, "r", encoding="utf-8", errors="replace") as f:
                content = f.read()
        except FileNotFoundError:
            messagebox.showerror("错误", f"找不到字幕文件: {srt_path}")
            return []

        blocks = content.strip().split("\n\n")
        for block in blocks:
            lines = block.strip().split("\n")
            if len(lines) >= 2:
                time_match = re.match(r"(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})", lines[1])
                if time_match:
                    start = self.time_to_seconds(time_match.group(1))
                    end = self.time_to_seconds(time_match.group(2))
                    text = " ".join(lines[2:])
                    subtitles.append({"start": start, "end": end, "text": text})
        subtitles.sort(key=lambda x: x["start"])
        return subtitles

    @staticmethod
    def time_to_seconds(time_str):
        h, m, s_ms = time_str.split(":")
        s, ms = s_ms.split(",")
        return int(h) * 3600 + int(m) * 60 + int(s) + int(ms) / 1000.0

    def generate_tts(self, text, output_file):
        service = self.tts_service.get()
        try:
            if service == "Google":
                # 从音色选项中提取语言
                voice_text = self.voice_type.get()
                lang_map = {
                    "中文(普通话)": "zh-cn",
                    "中文(粤语)": "zh-yue",
                    "英语(美国)": "en-us",
                    "英语(英国)": "en-uk",
                    "日语": "ja",
                    "韩语": "ko"
                }
                lang = lang_map.get(voice_text, "zh-cn")

                tts = gTTS(text=text, lang=lang)
                tts.save(output_file)
            elif service == "Tencent":
                # 检查是否选择了自定义音色ID
                if self.voice_type.get() == "自定义ID":
                    # 使用自定义输入框中的音色ID
                    try:
                        voice_id = int(self.custom_voice_id.get())
                    except ValueError:
                        raise ValueError("自定义音色ID必须是数字")
                else:
                    # 从音色选项中提取音色ID
                    voice_text = self.voice_type.get()
                    if '-' in voice_text:
                        voice_id = int(voice_text.split('-')[1].strip())
                    else:
                        # 如果格式不符合预期，尝试提取数字
                        match = re.search(r'\d+', voice_text)
                        if match:
                            voice_id = int(match.group())
                        else:
                            voice_id = 501001  # 默认值（大模型音色-智兰）

                # 尝试TTS生成，如果音色不支持会抛出异常
                try:
                    self.tencent_tts(text, output_file, voice_id)
                except Exception as e:
                    if "不受长文本语音合成支持" in str(e):
                        # 音色不支持，让用户选择处理方式
                        new_voice_id = self.handle_voice_error(voice_id, str(e))
                        if new_voice_id:
                            # 用户选择使用推荐音色，重新尝试
                            self.tencent_tts(text, output_file, new_voice_id)
                        else:
                            raise e
                    else:
                        raise e
        except Exception as e:
            raise RuntimeError(f"TTS生成失败 ({service}): {e}")

    def tencent_tts(self, text, output_file, voice_type):
        """使用腾讯云长文本语音合成接口"""
        secret_id = self.tencent_id.get()
        secret_key = self.tencent_key.get()
        app_id_str = self.tencent_appid.get()

        if not all([secret_id, secret_key, app_id_str]):
            raise ValueError("腾讯云配置不完整，请填写SecretId、SecretKey和AppId")

        # 长文本语音合成支持的音色ID列表（基于官方文档2025-08-15更新）
        supported_voices = [
            # 大模型音色（501系列）
            501000, 501001, 501002, 501003, 501004, 501005, 501006, 501007, 501008, 501009,
            # 大模型音色（601系列）
            601000, 601001, 601002, 601003, 601004, 601005, 601006, 601007, 601008, 601009,
            601010, 601011, 601012, 601013, 601014, 601015,
            # 精品音色（100510000和101系列）
            100510000, 101001, 101002, 101003, 101004, 101005, 101006, 101008, 101009, 101010,
            101011, 101012, 101013, 101014, 101015, 101016, 101017, 101018, 101019, 101020,
            101021, 101022, 101023, 101024, 101025, 101026, 101027, 101028, 101029, 101030,
            101031, 101032, 101033, 101034, 101035, 101040, 101050, 101051, 101052, 101053,
            101054, 101055, 101056, 101057, 101080, 101081,
            # 301系列精品音色
            301000, 301001, 301002, 301003, 301004, 301005, 301006, 301007, 301008, 301009,
            301010, 301011, 301012, 301013, 301014, 301015, 301016, 301017, 301018, 301019,
            301020, 301021, 301022, 301023, 301024, 301025, 301026, 301027, 301028, 301029,
            301030, 301031, 301032, 301033, 301034, 301035, 301036, 301037, 301038, 301039,
            301040, 301041,
            # 标准音色（10510000和1系列）
            10510000, 1001, 1002, 1003, 1004, 1005, 1008, 1009, 1010, 1017, 1018, 1050, 1051
        ]

        # 如果音色ID不在支持列表中，抛出错误并暂停程序
        if voice_type not in supported_voices:
            error_msg = f"音色ID {voice_type} 不受长文本语音合成支持！\n\n支持的音色类型：\n- 大模型音色：501xxx, 601xxx系列\n- 精品音色：100510000, 101xxx, 301xxx系列\n- 标准音色：10510000, 1xxx系列\n\n请更改音色后重试。"
            raise ValueError(error_msg)

        try:
            # 第一步：创建长文本语音合成任务
            task_id = self.create_tts_task(text, voice_type, secret_id, secret_key)
            print(f"创建TTS任务成功，任务ID: {task_id}")

            # 第二步：轮询获取合成结果
            audio_url = self.wait_for_tts_result(task_id, secret_id, secret_key)
            print(f"TTS合成完成，音频URL: {audio_url}")

            # 第三步：下载音频文件
            self.download_audio(audio_url, output_file)
            print(f"音频文件下载完成: {output_file}")

        except Exception as e:
            # 如果是音色不支持的错误，暂停程序并让用户选择
            error_msg = str(e)
            if ("VoiceType" in error_msg or "parameter VoiceType" in error_msg or
                "不受长文本语音合成支持" in error_msg):
                self.handle_voice_error(voice_type, error_msg)
            else:
                raise e

    def handle_voice_error(self, voice_type, error_msg):
        """处理音色不支持错误，暂停程序并让用户选择"""
        import tkinter.messagebox as msgbox

        # 构建错误信息
        full_error_msg = f"音色错误：{error_msg}\n\n当前音色ID: {voice_type}\n\n请选择处理方式："

        # 显示错误对话框，让用户选择
        result = msgbox.askyesnocancel(
            "音色不支持错误",
            full_error_msg,
            detail="点击'是'：停止处理并返回设置界面\n点击'否'：使用推荐音色继续\n点击'取消'：终止程序"
        )

        if result is True:  # 用户选择"是" - 停止处理
            self.status_var.set("处理已暂停，请更改音色设置")
            raise RuntimeError("用户选择停止处理，请更改音色设置后重试")
        elif result is False:  # 用户选择"否" - 使用推荐音色
            recommended_voice = 501001  # 推荐使用智兰(资讯女声)
            self.status_var.set(f"使用推荐音色 {recommended_voice} 继续处理...")
            print(f"用户选择使用推荐音色 {recommended_voice} 继续处理")
            # 更新界面上的音色选择
            self.voice_type.set(f"智兰(资讯女声) - {recommended_voice}")
            return recommended_voice
        else:  # 用户选择"取消" - 终止程序
            self.status_var.set("用户取消处理")
            raise RuntimeError("用户取消处理")

    def create_tts_task(self, text, voice_type, secret_id, secret_key):
        """创建长文本语音合成任务"""
        import json

        endpoint = "tts.tencentcloudapi.com"
        action = "CreateTtsTask"
        version = "2019-08-23"

        # 请求参数
        params = {
            "Text": text,
            "VoiceType": voice_type,
            "Volume": 0,
            "Speed": 0,
            "ProjectId": 0,
            "ModelType": 1,
            "PrimaryLanguage": 1,
            "SampleRate": 16000,
            "Codec": "mp3"
        }

        # 使用腾讯云API 3.0签名方法
        headers = self.get_tencent_headers(action, version, params, secret_id, secret_key)

        # 发送请求
        url = f"https://{endpoint}/"
        response = requests.post(url, headers=headers, data=json.dumps(params))

        if response.status_code != 200:
            raise ConnectionError(f"创建TTS任务失败: {response.status_code} - {response.text}")

        result = response.json().get("Response", {})
        if "Error" in result:
            error_msg = result['Error']['Message']
            raise ValueError(f"腾讯云TTS错误: {error_msg} (RequestId: {result.get('RequestId')})")

        return result["Data"]["TaskId"]

    def wait_for_tts_result(self, task_id, secret_id, secret_key, max_wait_time=180):
        """轮询获取TTS合成结果"""
        import json

        endpoint = "tts.tencentcloudapi.com"
        action = "DescribeTtsTaskStatus"
        version = "2019-08-23"

        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            params = {"TaskId": task_id}
            headers = self.get_tencent_headers(action, version, params, secret_id, secret_key)

            url = f"https://{endpoint}/"
            response = requests.post(url, headers=headers, data=json.dumps(params))

            if response.status_code != 200:
                raise ConnectionError(f"查询TTS任务状态失败: {response.status_code} - {response.text}")

            result = response.json().get("Response", {})
            if "Error" in result:
                error_msg = result['Error']['Message']
                raise ValueError(f"腾讯云TTS错误: {error_msg} (RequestId: {result.get('RequestId')})")

            data = result.get("Data", {})
            status = data.get("Status", 0)

            if status == 2:  # 合成完成
                return data.get("ResultUrl", "")
            elif status == 3:  # 合成失败
                error_msg = data.get("ErrorMsg", "未知错误")
                raise ValueError(f"TTS合成失败: {error_msg}")

            # 等待2秒后重试
            time.sleep(2)

        raise TimeoutError("TTS合成超时，请稍后重试")

    def get_tencent_headers(self, action, version, params, secret_id, secret_key):
        """生成腾讯云API 3.0请求头"""
        import json
        import hashlib
        import hmac
        from datetime import datetime

        # 基本信息
        service = "tts"
        host = "tts.tencentcloudapi.com"
        algorithm = "TC3-HMAC-SHA256"
        timestamp = int(time.time())
        date = datetime.utcfromtimestamp(timestamp).strftime("%Y-%m-%d")

        # 步骤1：拼接规范请求串
        http_request_method = "POST"
        canonical_uri = "/"
        canonical_querystring = ""
        canonical_headers = f"content-type:application/json; charset=utf-8\nhost:{host}\n"
        signed_headers = "content-type;host"
        payload = json.dumps(params)
        hashed_request_payload = hashlib.sha256(payload.encode("utf-8")).hexdigest()
        canonical_request = f"{http_request_method}\n{canonical_uri}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{hashed_request_payload}"

        # 步骤2：拼接待签名字符串
        credential_scope = f"{date}/{service}/tc3_request"
        hashed_canonical_request = hashlib.sha256(canonical_request.encode("utf-8")).hexdigest()
        string_to_sign = f"{algorithm}\n{timestamp}\n{credential_scope}\n{hashed_canonical_request}"

        # 步骤3：计算签名
        def sign(key, msg):
            return hmac.new(key, msg.encode("utf-8"), hashlib.sha256).digest()

        secret_date = sign(("TC3" + secret_key).encode("utf-8"), date)
        secret_service = sign(secret_date, service)
        secret_signing = sign(secret_service, "tc3_request")
        signature = hmac.new(secret_signing, string_to_sign.encode("utf-8"), hashlib.sha256).hexdigest()

        # 步骤4：拼接Authorization
        authorization = f"{algorithm} Credential={secret_id}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}"

        # 返回请求头
        return {
            "Authorization": authorization,
            "Content-Type": "application/json; charset=utf-8",
            "Host": host,
            "X-TC-Action": action,
            "X-TC-Timestamp": str(timestamp),
            "X-TC-Version": version
        }

    def download_audio(self, url, output_file):
        """下载音频文件"""
        response = requests.get(url)
        if response.status_code == 200:
            with open(output_file, 'wb') as f:
                f.write(response.content)
        else:
            raise ConnectionError(f"下载音频文件失败: {response.status_code}")

    def concat_videos(self, clip_paths, output_path):
        """合并视频片段，支持GPU加速"""
        list_file_path = os.path.join(os.path.dirname(output_path), "concat_list.txt")
        with open(list_file_path, "w", encoding='utf-8') as f:
            for path in clip_paths:
                safe_path = path.replace("'", "'\\''")
                f.write(f"file '{safe_path}'\n")

        try:
            # 获取GPU加速参数
            gpu_params = self.get_ffmpeg_gpu_params()

            # 使用GPU加速合并视频
            cmd = (["ffmpeg", "-f", "concat", "-safe", "0", "-i", list_file_path] +
                   gpu_params['hwaccel'] + ["-c:v", "copy", "-c:a", "copy", "-y", output_path])

            subprocess.run(cmd, check=True, capture_output=True)
            print(f"✓ 视频合并完成 (GPU加速: {self.gpu_acceleration.get()})")

        except subprocess.CalledProcessError as e:
            print(f"合并视频失败: {e.stderr.decode()}")
            with open(list_file_path, "r", encoding='utf-8') as f:
                print(f"Concat list file content:\n{f.read()}")
            raise
        finally:
            if os.path.exists(list_file_path):
                os.remove(list_file_path)


if __name__ == "__main__":
    try:
        # 检查psutil依赖
        try:
            import psutil
            print("CPU监控功能可用")
        except ImportError:
            print("警告: 未安装psutil，CPU使用率监控功能将被禁用")
            print("建议安装: pip install psutil")

        # 在Windows上隐藏命令行窗口
        startupinfo = None
        if os.name == 'nt':
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW

        subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True,
                      startupinfo=startupinfo, encoding='utf-8', errors='ignore', timeout=10)
        print("FFmpeg检测成功")
    except subprocess.TimeoutExpired:
        messagebox.showerror("依赖错误", "FFmpeg检测超时！\n请检查FFmpeg安装是否正确。")
        exit(1)
    except (subprocess.CalledProcessError, FileNotFoundError):
        messagebox.showerror("依赖错误", "未找到FFmpeg！\n请先安装FFmpeg并将其添加到系统环境变量 (PATH) 中。")
        exit(1)
    except Exception as e:
        messagebox.showerror("依赖错误", f"FFmpeg检测失败: {e}\n请检查FFmpeg安装。")
        exit(1)

    root = tk.Tk()
    app = VideoEditorApp(root)
    root.mainloop()