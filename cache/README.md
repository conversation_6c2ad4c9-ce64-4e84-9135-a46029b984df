# 缓存目录说明

这个目录用于存储程序运行时产生的临时文件和缓存数据。

## 目录结构

```
cache/
├── README.md                    # 本说明文件
├── temp_xxxxxxxx/              # 视频处理临时目录
│   ├── video_only.mp4          # 提取的纯视频文件
│   ├── raw_clip_*.mp4          # 切割的视频片段
│   ├── audio_*.mp3             # TTS生成的音频文件
│   ├── audio_*.aac             # 转换后的AAC音频
│   ├── final_clip_*.mp4        # 最终合成的片段
│   ├── padded_audio_*.aac      # 补充静音的音频
│   ├── spedup_audio_*.aac      # 加速处理的音频
│   ├── silence.aac             # 静音片段
│   └── concat_list.txt         # FFmpeg合并列表
├── voice_test.mp3              # 音色测试文件
└── test.mp4                    # GPU性能测试文件
```

## 文件说明

- **temp_xxxxxxxx/**: 每次视频处理都会创建一个唯一的临时目录
- **voice_test.mp3**: 音色测试时生成的临时音频文件
- **test.mp4**: GPU性能基准测试时生成的测试视频

## 清理说明

- 程序正常结束时会自动清理临时文件
- 可以通过界面上的"清理缓存"按钮手动清理
- 也可以直接删除整个cache目录来清理所有缓存

## 注意事项

- 此目录下的文件都是临时文件，可以安全删除
- 处理大视频文件时，此目录可能占用较多磁盘空间
- 建议定期清理以释放磁盘空间
