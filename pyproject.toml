[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ai-video-editor"
version = "0.1.0"
description = "AI-powered video editing software with subtitle rewriting and TTS"
authors = [
    {name = "AI Video Editor Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: Microsoft :: Windows",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Multimedia :: Video",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
    "PySide6>=6.6.0",
    "opencv-python>=4.8.0",
    "ffmpeg-python>=0.2.0",
    "requests>=2.31.0",
    "aiohttp>=3.8.0",
    "pydantic>=2.0.0",
    "python-dotenv>=1.0.0",
    "loguru>=0.7.0",
    "pyaudio>=0.2.11",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.11.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
    "pytest-cov>=4.1.0",
]
docs = [
    "sphinx>=7.1.0",
    "sphinx-rtd-theme>=1.3.0",
]

[project.scripts]
ai-video-editor = "main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["core*", "gui*", "utils*", "config*"]
exclude = ["tests*", "temp*", "output*", "logs*"]

[tool.black]
line-length = 88
target-version = ["py39"]
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | temp
  | output
  | logs
)/
'''

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true
exclude = [
    "temp/",
    "output/",
    "logs/",
    "venv/",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short --strict-markers"
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "temp",
    "output", 
    "logs",
    "venv",
    ".venv",
    "build",
    "dist",
]

[tool.coverage.run]
source = ["core", "gui", "utils", "config"]
omit = [
    "*/tests/*",
    "*/temp/*",
    "*/output/*",
    "*/logs/*",
    "*/venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
