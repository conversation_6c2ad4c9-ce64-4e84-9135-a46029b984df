# AI Video Editor

基于AI的智能视频剪辑软件，支持字幕改写、TTS配音和音画同步。

## 功能特性

- 🎬 **智能视频处理**: 基于ffmpeg的视频分割、合并和变速处理
- 🤖 **AI字幕改写**: 使用DeepSeek模型进行智能文案改写和润色
- 🎙️ **TTS配音**: 集成阿里云TTS服务，支持多种音色
- 🎵 **音画同步**: 智能音频与视频时长匹配
- 📝 **字幕编辑**: 可视化字幕编辑器，支持手动调整
- 🎨 **多风格支持**: 支持多种改写风格，可自定义prompt

## 系统要求

- Windows 11
- Python 3.9+
- FFmpeg (需要安装并添加到PATH)

## 安装说明

1. 克隆项目
```bash
git clone <repository-url>
cd AI_editor
```

2. 创建虚拟环境
```bash
python -m venv venv
venv\Scripts\activate
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 配置API密钥
- 复制 `config/api_config.example.json` 为 `config/api_config.json`
- 填入DeepSeek和阿里云TTS的API密钥

## 使用方法

1. 启动应用
```bash
python main.py
```

2. 上传视频文件和字幕文件
3. 选择改写风格
4. 开始AI处理流程
5. 手动编辑确认字幕
6. 导出最终结果

## 项目结构

```
AI_editor/
├── main.py                 # 主程序入口
├── config/                 # 配置文件
│   ├── prompts/           # AI改写风格配置
│   │   ├── style_1/       # 风格1
│   │   ├── style_2/       # 风格2
│   │   └── style_3/       # 风格3
│   └── settings.py        # 全局配置
├── core/                   # 核心业务模块
│   ├── video_processor.py # 视频处理
│   ├── subtitle_processor.py # 字幕处理
│   ├── ai_service.py      # AI服务
│   ├── tts_service.py     # TTS服务
│   └── workflow_manager.py # 工作流管理
├── gui/                    # GUI界面
│   ├── main_window.py     # 主窗口
│   ├── widgets/           # 自定义组件
│   └── dialogs/           # 对话框
├── utils/                  # 工具模块
├── temp/                   # 临时文件
├── output/                 # 输出文件
└── logs/                   # 日志文件
```

## 开发说明

### 代码规范
- 使用Black进行代码格式化
- 使用MyPy进行类型检查
- 使用Pytest进行单元测试

### 运行测试
```bash
pytest tests/
```

### 代码格式化
```bash
black .
```

### 类型检查
```bash
mypy .
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request。

## 更新日志

### v0.1.0 (2025-08-19)
- 初始版本
- 基础架构搭建
- 核心模块设计完成
